import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import <PERSON><PERSON><PERSON><PERSON>per from "@/utils/ApiHelper";
import CommonHelper from "@/utils/CommonHelper";
import { usePersistKioskPosStore } from "../../storePersist";

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }
      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  paymentDetail: null,
  loading: false,
  error: null,
  ref_Loading: null,
  ref_MySnackbar: null,
  ref_ModalConfirmation: null,

  onLoading: (show) => {
    if (get().ref_Loading.current) {
      if (show) {
        get().ref_Loading.current.onShowDialog();
      } else {
        get().ref_Loading.current.onCloseDialog();
      }
    }
  },
  onNotify: (message, severity) => {
    if (get().ref_MySnackbar.current) {
      get().ref_MySnackbar.current.onNotify(message, severity);
    }
  },
  fetchPaymentDetail: async (code) => {
    try {
      get().onLoading(true);
      const response = await ApiHelper.get("kiosk/kiosk/pos/payment/detail", {
        number: code,
        customer_id: usePersistKioskPosStore.getState().objCustomer.id,
      });
      get().onLoading(false);
      if (response.status === 200) {
        set({ paymentDetail: response.results.data, loading: false });
        return response.results.data;
      } else {
        get().onNotify(response.message, "error");
        set({ error: response.message, loading: false });
        return null;
      }
    } catch (error) {
      get().onLoading(false);
      get().onNotify(error.message, "error");
      set({ error: error.message, loading: false });
      return null;
    }
  },
});

export const usePaymentDetailStore = create((...a) => ({
  ...page(...a),
}));

const useTrackedPaymentDetailStore = createTrackedSelector(
  usePaymentDetailStore
);

export default useTrackedPaymentDetailStore;
