import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import CommonHelper from "@/utils/CommonHelper";
import { usePersistKioskPosStore } from "../storePersist";
import { numberFormatIdToNumber } from "@/components/libs/Input";
import ApiHelper from "@/utils/ApiHelper";
import Router from "next/router";

const DEFAULT_INPUTS = {
  selectedVoucher: null,
  voucher_verif_code: "",
  affiliate_name: "",
  affiliate_code: "",

  sub_total: 0,
  nominal_discount_label: "0",
  nominal_discount: 0,
  nominal_transaction: 0,
  selectedPaymentMethod: null,
  mdr: 0,
  service_fee: 0,
  payment_mdr: 0,
  payment_poin_redeem: 0,
  payment_total: 0, // nominal_transaction + payment_mdr
  payment_nominal_input: 0, // Nilai yang dibayar
  payment_nominal_return: 0, // Kembalian dari nilai yang dibayar
};

const DEFAULT_CART = [
  {
    id: 1,
    image_url: "https://www.thenextsole.com/storage/images/AQ2731-104.png",
    name: "Nike Joyride Run Flyknit Women's Running Shoes",
    price: 12500000,
    unit: "pcs",
    qty: 1,
    desc: "",
  },
  {
    id: 2,
    image_url:
      "https://static.nike.com/a/images/t_default/89a9429b-c0d9-4699-b897-c261fddaf221/W+AIR+MAX+270.png",
    name: "Nike Air Max 270 Women's Shoes",
    price: 6250000,
    unit: "pcs",
    qty: 1,
    desc: "",
  },
  {
    id: 3,
    image_url:
      "https://static.nike.com/a/images/t_default/efcb2629-34c7-484a-9a76-86959a52b272/W+NIKE+ZOOMX+VAPORFLY+NEXT%25+3.png",
    name: "Nike Vaporfly 3 Women's Road Racing Shoes",
    price: 6250000,
    unit: "pcs",
    qty: 1,
    desc: "",
  },
];

const DEFAULT_PAYMENT_METHODS = [
  {
    id: 1,
    name: "Bayar Dikasir",
    image_url:
      "https://www.transparentpng.com/thumb/money/kxYuge-money-png-nowskills-apprenticeships.png",
  },
  {
    id: 2,
    name: "QRIS",
    image_url:
      "https://developers.bri.co.id/sites/default/files/2023-02/qris-mpm-dinamis.png",
  },
  {
    id: 3,
    name: "Gopay",
    image_url:
      "https://play-lh.googleusercontent.com/bDgy8Qctp4bT41ioDZvrdOOVYvqscOwoKfBYFLUolNr9m7r0JiSXfzfPRtbZCZ-cWRw",
  },
  {
    id: 4,
    name: "ShopeePay",
    image_url:
      "https://i.pinimg.com/736x/d0/19/16/d019163d861908ed0046391ebfa42ce1.jpg",
  },
  {
    id: 5,
    name: "Dana",
    image_url:
      "https://blogger.googleusercontent.com/img/a/AVvXsEj10i8Btb-dl9eXZNX3Wyn2g_fM28L9bHQOJ37eW7eHhVqNhYdndDf40Ba4Zm88GyKR1bhvbtWmF-fY61RbMnHhP-ea9wJbrZYlg1BLD5xqToO4KE85LbfHbZf6J7R8MdkKcICrdyr8WpBFGw_ujAM-zcmWy8ssLC4Sv8C1nzrL1jtrsNLzyqm6Rr4T=s16000",
  },
  {
    id: 6,
    name: "Transfer Bank",
    image_url:
      "https://img.pikbest.com/png-images/********/payment-bank-transfer-icon-illustration_10612469.png!sw800",
  },
  {
    id: 7,
    name: "Payment Link",
    image_url:
      "https://cdn2.iconfinder.com/data/icons/untact-brepigy/128/ic_cards-512.png",
  },
  {
    id: 8,
    name: "Lainnya",
    image_url:
      "https://static.vecteezy.com/system/resources/thumbnails/009/315/289/small/3d-credit-card-money-financial-security-for-online-shopping-online-payment-credit-card-with-payment-protection-concept-3d-render-for-business-finance-shopping-with-mobile-security-concept-free-png.png",
  },
];

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }
      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  // State properties
  user: null,
  selected_aol: null,
  selected_branch: null,
  // arrPaymentMethods: structuredClone(DEFAULT_PAYMENT_METHODS),
  inputs: structuredClone(DEFAULT_INPUTS),
  errors: {},
  isProductUseSerial: false,

  isAffiliateOTPSent: false,

  isGetPaymentMethods: false,
  arrPaymentMethods: [],

  // Refs
  ref_Loading: null,
  ref_MySnackbar: null,
  ref_ModalConfirmation: null,
  ref_paymentForm: null,
  ref_ModalVoucher: null,
  ref_ModalAffiliate: null,

  onLoading: (show, timeout = 300) => {
    if (get().ref_Loading.current) {
      if (show) {
        get().ref_Loading.current.onShowDialog();
      } else {
        setTimeout(() => {
          get().ref_Loading.current.onCloseDialog();
        }, timeout);
      }
    }
  },
  onNotify: (message, severity) => {
    if (get().ref_MySnackbar.current?.onNotify) {
      get().ref_MySnackbar.current?.onNotify(message, severity);
    }
  },
  onInit: async () => {
    // task
    await Promise.all([
      CommonHelper.task("onVerifyData", async (resolve) => {
        get().onVerifyData(resolve);
      }),
      CommonHelper.task("onFetchPaymentMethod", async (resolve) => {
        get().onFetchPaymentMethod(resolve);
      }),
    ]);

    get().onLoading(false, 0);
  },

  // Actions
  onTextInputListeners: (text, input) => {
    set((state) => {
      const newInputs = { ...state.inputs, [input]: text };
      return { inputs: newInputs };
    });
  },

  onTextErrorListeners: (error, input) => {
    set((state) => ({
      errors: { ...state.errors, [input]: error },
    }));
  },

  onCalculateGrandTotal: () => {
    set((state) => {
      const inputs = { ...state.inputs };
      inputs.payment_total =
        Number(inputs.nominal_transaction) +
        Number(inputs.payment_mdr) -
        Number(inputs.payment_poin_redeem);
      return { inputs };
    });
  },

  onValidateListeners: () => {
    const { ref_Loading, ref_paymentForm } = get();

    get().ref_ModalConfirmation.current.onShowDialog("custom", {
      text: {
        title: "Konfirmasi Pembayaran",
        action: "Ya",
        info: <>Apakah Anda yakin akan melakukan pembayaran?</>,
      },
      render: (formData) => {
        return (
          <div style={{ textAlign: "center" }}>
            Apakah Anda yakin akan melakukan pembayaran?
          </div>
        );
      },
      icon: (
        <>
          <i className="ph ph-bold ph-check-circle"></i>
        </>
      ),
      classBody: "flex flex-col gap-4 items-center justify-center",
      onConfirmed: (formType, formData, formIndex) => {
        get().ref_ModalConfirmation.current.onCloseDialog();
        get().onSubmitPayment();
        // if (ref_Loading?.current) {
        //   ref_Loading.current.onShowDialog();
        //   setTimeout(() => {
        //     ref_Loading.current.onCloseDialog();
        //     if (ref_paymentForm?.current) {
        //       ref_paymentForm.current.onShowDialog("kiosk", null);
        //     }
        //   }, 500);
        // }
      },
    });
  },

  onSelectPaymentMethod: (paymentMethod) => {
    set((state) => {
      const inputs = { ...state.inputs };
      inputs.selectedPaymentMethod = paymentMethod;
      return { inputs };
    });
    get().onCalculateTransaction();
  },

  onChangeTotalDiscount: (value) => {
    set((state) => {
      const inputs = { ...state.inputs };
      inputs.nominal_discount = Number(numberFormatIdToNumber(value));
      inputs.nominal_discount_label = value;

      if (inputs.nominal_discount < 0) {
        inputs.nominal_discount = 0;
        inputs.nominal_discount_label = "0";
      }

      return { inputs };
    });
    get().onCalculateTransaction();
  },

  onVerifyData: async (resolve) => {
    let apiEndPoint = "kiosk/kiosk/pos/payment/first-verify";

    let params = {
      aol_id: get().selected_aol.aol_id,
      aol_session_database: get().selected_aol.database,
      branch_accurate_id: get().selected_branch.accurate_id,
      arrCart: usePersistKioskPosStore.getState().arrCart,
    };
    let response = await ApiHelper.post(apiEndPoint, params);
    let isSuccess = false;
    let isProductUseSerial = false;
    if (response.status === 200) {
      isSuccess = true;
      usePersistKioskPosStore.getState().setState({
        arrCart: response.results.data.arrCart,
      });
      isProductUseSerial = response.results.data.isProductUseSerial;
      // get().onNotify(response?.message || "Berhasil Verifikasi", "success");
    } else {
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
      // setTimeout(() => {
      //   Router.replace({ pathname: "/pos" });
      // }, 500);
    }

    set({ isProductUseSerial });

    if (isSuccess) {
      // get().onCalculateCart();
      usePersistKioskPosStore.getState().onCalculateCart(true);
      get().onCalculateTransaction();
    }

    if (resolve) {
      resolve();
    }
  },
  onFetchPaymentMethod: async (resolve) => {
    set({ isGetPaymentMethods: true });
    let params = {
      pagination_bool: false,
    };
    let response = await ApiHelper.get(
      "kiosk/kiosk/pos/payment/config/data/payment-method",
      params
    );
    let inputs = structuredClone(get().inputs);
    if (response.status === 200) {
      inputs.selectedPaymentMethod =
        response.results.data[0] !== undefined
          ? response.results.data[0]
          : null;

      set({ arrPaymentMethods: response.results.data, inputs });
    } else {
      inputs.selectedPaymentMethod = null;
      set({ arrPaymentMethods: [], selectedPaymentMethod: null });
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    set({ isGetPaymentMethods: false });

    if (resolve) {
      resolve();
    }
  },

  onCalculateTransaction: () => {
    set((state) => {
      const inputs = structuredClone(state.inputs);

      const totalProductSellingPrice =
        usePersistKioskPosStore.getState().totalProductSellingPrice;

      inputs.sub_total = totalProductSellingPrice;

      // discount voucher
      if (inputs.selectedVoucher?.id) {
        inputs.sub_total -= Math.abs(
          inputs.selectedVoucher?.product_selling_price
        );
      }

      inputs.nominal_transaction = inputs.sub_total - inputs.nominal_discount;

      inputs.mdr = 0;
      inputs.service_fee = 0;
      if (inputs.selectedPaymentMethod?.id) {
        if (inputs.selectedPaymentMethod?.id === 1) {
          inputs.payment_mdr = 0;
        } else {
          // calculate mdr
          inputs.mdr = Number(inputs.selectedPaymentMethod?.mdr);
          inputs.service_fee = Number(
            inputs.selectedPaymentMethod?.service_fee
          );
          if (inputs.selectedPaymentMethod?.mdr_type === "percent") {
            inputs.mdr = (inputs.nominal_transaction * inputs.mdr) / 100;
          }
          if (inputs.selectedPaymentMethod?.admin_fee_type === "percent") {
            inputs.service_fee =
              (inputs.nominal_transaction * inputs.service_fee) / 100;
          }

          inputs.payment_mdr = inputs.mdr + inputs.service_fee;
        }
      }

      inputs.payment_total =
        Number(inputs.nominal_transaction) + Number(inputs.payment_mdr);

      return { inputs };
    });
  },
  onSubmitPayment: async () => {
    // get().onLoading(true);

    let inputsPayment = structuredClone(get().inputs);
    inputsPayment.sub_total =
      usePersistKioskPosStore.getState().totalProductSellingPrice;

    let arrCart = structuredClone(usePersistKioskPosStore.getState().arrCart);
    // delete where stock_balance <= 0
    arrCart = arrCart.filter((item) => item.stock_balance > 0);

    let params = {
      aol_id: get().selected_aol.aol_id,
      aol_session_database: get().selected_aol.database,
      branch_accurate_id: get().selected_branch.accurate_id,
      objCustomer: usePersistKioskPosStore.getState().objCustomer,
      arrCart: usePersistKioskPosStore.getState().arrCart,
      inputsPayment,
      arrVoucher: get().inputs.selectedVoucher?.id
        ? [get().inputs.selectedVoucher]
        : [],
      isProductUseSerial: get().isProductUseSerial,
    };

    let response = await ApiHelper.post(
      "kiosk/kiosk/pos/payment/process",
      params
    );

    if (response.status === 200) {
      get().onNotify("Berhasil Proses Pembayaran", "success");
    } else {
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    get().onLoading(false);
  },

  onSendAffiliateOTP: async () => {
    get().onLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // For demonstration, always succeed. In a real app, check API response.
    const success = true;

    if (success) {
      set({ isAffiliateOTPSent: true });
      get().onNotify("OTP sent to affiliate", "success");
    } else {
      get().onNotify("Failed to send OTP. Please try again.", "error");
    }
    get().onLoading(false);
  },

  // Reset store to initial state
  onReset: () => {
    set({
      arrPaymentMethods: [...DEFAULT_PAYMENT_METHODS],
      inputs: { ...DEFAULT_INPUTS },
      errors: {},
    });
  },
});

const modal = (set, get) => ({
  onShowModalVoucher: () => {
    get().ref_ModalVoucher.current.onShowDialog("list", {
      selected: [get().inputs.selectedVoucher],
      // objCustomer
      customer_id: usePersistKioskPosStore.getState().objCustomer.id,
      aol_id: get().selected_aol.aol_id,
      aol_session_database: get().selected_aol.database,
      status: "Belum Klaim",
      is_claimed_bool: false,
      apiParams: {
        branch_accurate_id: get().selected_branch.accurate_id,
      },
    });
  },
  onSelectVoucher: (selected) => {
    get().setState({
      inputs: {
        ...get().inputs,
        selectedVoucher: selected[0],
        voucher_verif_code: "",
      },
    });
    get().onCalculateTransaction();
    get().onGenerateVoucherVerifCode();
  },
  onGenerateVoucherVerifCode: async () => {
    if (!get().inputs.selectedVoucher) {
      get().onNotify("Silahkan pilih voucher terlebih dahulu.", "warning");
      return;
    }

    get().onLoading(true);

    let params = {
      id: get().inputs.selectedVoucher.id,
    };
    let response = await ApiHelper.post("kiosk/kiosk/voucher/verify", params);

    if (response.status === 200) {
      // get().onNotify(response?.message || "Berhasil Generate", "success");
      if (response.results.data.verif_code) {
        let inputs = structuredClone(get().inputs);
        inputs.voucher_verif_code = response.results.data.verif_code;
        set({
          inputs,
        });
      }
    } else {
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    get().onLoading(false);
  },
  onShowModalAffiliate: () => {
    get().ref_ModalAffiliate.current.onShowDialog(
      "",
      {},
      {},
      {
        code: get().inputs.affiliate_code,
      }
    );
  },
  onChangeAffiliate: (code, name) => {
    get().setState({
      inputs: {
        ...get().inputs,
        affiliate_code: code,
        affiliate_name: name,
      },
    });
  },
});

export const usePaymentStore = create((...a) => ({
  ...page(...a),
  ...modal(...a),
}));

const useTrackedPaymentStore = createTrackedSelector(usePaymentStore);

export default useTrackedPaymentStore;
