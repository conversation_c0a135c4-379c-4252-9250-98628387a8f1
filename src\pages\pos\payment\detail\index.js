import { useEffect, useRef } from "react";
import { useRouter } from "next/router";
import useTrackedPaymentDetailStore, {
  usePaymentDetailStore,
} from "@/store/kiosk/pos/payment/detail/store";
import usePersistKioskPosStore from "@/store/kiosk/pos/storePersist";
import styles from "@/styles/Pos.module.css";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import Constants from "@/utils/Constants";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import CommonHelper from "@/utils/CommonHelper";

const KioskPaymentDetailPage = ({
  isAuthLoading,
  isLoggedIn,
  user,
  selected_aol,
  selected_branch,
}) => {
  const router = useRouter();
  const { objCustomer } = usePersistKioskPosStore();
  const { paymentDetail, loading, error, fetchPaymentDetail } =
    useTrackedPaymentDetailStore();

  const ref_Loading = useRef(null);
  const ref_MySnackbar = useRef(null);
  const ref_ModalConfirmation = useRef(null);

  useEffect(() => {
    usePaymentDetailStore.getState().setState({
      ref_Loading: ref_Loading,
      ref_MySnackbar: ref_MySnackbar,
      ref_ModalConfirmation: ref_ModalConfirmation,
    });
  }, []);

  // Redirect if no customer selected
  useEffect(() => {
    if (!isAuthLoading && isLoggedIn) {
      if (!objCustomer?.id) {
        router.push("/pos");
      }

      const params = CommonHelper.getAllURLParams();
      if (params?.code) {
        fetchPaymentDetail(params.code);
      }
    }
  }, [isAuthLoading, isLoggedIn, objCustomer]);

  if (!objCustomer) return null; // Already redirecting

  return (
    <>
      <div className={styles.ct_containers}>
        <div className={styles.contents}>
          <RenderPaymentDetail />
        </div>
      </div>

      {/* Loading dialog */}
      <Loading ref={ref_Loading} />
      <MySnackbar ref={ref_MySnackbar} />
      <ModalConfirmation ref={ref_ModalConfirmation} />
    </>
  );
};

const RenderPaymentDetail = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  // "paymentDetail": {
  // "id": "456",
  // "number": "KIOSK-JL-BBS-2025-06-12-00005",
  // "date": "2025-06-12",
  // "accurate_id": "0",
  // "aol_id": "3",
  // "aol_session_database": "1873550",
  // "shift_id": "0",
  // "customer_id": "24995",
  // "customer_accurate_id": "3048",
  // "customer_aol_id": "3",
  // "customer_aol_session_database": "1873550",
  // "customer_name": "Betty F",
  // "customer_code": "ELS250521-00003",
  // "customer_email": "<EMAIL>",
  // "customer_whatsapp": "6282143214321",
  // "customer_mobilephone": "",
  // "customer_businessphone": "",
  // "customer_category_id": "35",
  // "customer_category_accurate_id": "51",
  // "customer_category_name": "Umum",
  // "employee_id": "0",
  // "employee_accurate_id": "0",
  // "employee_name": "",
  // "employee_salutation": "",
  // "employee_code": "",
  // "employee_mobilephone": "",
  // "employee_whatsapp_phone": "",
  // "employee_address": "",
  // "employee_branch_accurate_id": "0",
  // "affiliate_id": "0",
  // "affiliate_name": "",
  // "affiliate_code": "",
  // "total_product_item": "1",
  // "total_product_purchase_price": "7500000.000000",
  // "total_product_selling_price": "7500000.000000",
  // "total_product_discount_nominal": "0.000000",
  // "total_discount_percent": "0",
  // "total_discount_nominal": "0.000000",
  // "total_nett_price": "7500000.000000",
  // "charge_percent": "0.00",
  // "charge_nominal": "0.000000",
  // "tax_percent": "0.00",
  // "tax_nominal": "0.000000",
  // "other_cost_label": "",
  // "other_cost_nominal": "0.000000",
  // "added_round_nominal_grand_total": "0.000000",
  // "grand_total": "7500000.000000",
  // "paid_nominal": "7500000.000000",
  // "status": "Menunggu Verifikasi"
  // }

  if (!paymentDetail) return null;

  return (
    <div className={styles.payment_detail_container}>
      <h2 className={styles.subtitle}>Payment Details</h2>
      <div className={styles.detail_row}>
        <span>Code:</span>
        <span>{paymentDetail.code}</span>
      </div>
    </div>
  );
};

export default AuthWrapper(KioskPaymentDetailPage, {
  redirectTo: Constants.webUrl.login,
});
